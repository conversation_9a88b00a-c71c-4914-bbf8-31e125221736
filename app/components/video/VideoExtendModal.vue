<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  modelValue: boolean
  onVideoSelected?: (video: any) => void
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'video-selected': [video: any]
}>()

const { t } = useI18n()
const historyStore = useHistoryStore()
const { histories, loadings } = storeToRefs(historyStore)

const selectedVideo = ref<any>(null)
const searchQuery = ref('')

// Computed property to show modal state
const isOpen = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// Filter video histories only
const videoHistories = computed(() => {
  return histories.value.filter(history => 
    history.type === 'video' && 
    history.status === 2 && // Only completed videos
    history.generated_video && 
    history.generated_video.length > 0
  )
})

// Filter histories based on search query
const filteredVideoHistories = computed(() => {
  if (!searchQuery.value.trim()) {
    return videoHistories.value
  }
  
  const query = searchQuery.value.toLowerCase()
  return videoHistories.value.filter(history =>
    history.input_text?.toLowerCase().includes(query) ||
    history.model_name?.toLowerCase().includes(query)
  )
})

// Load video histories when modal opens
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadVideoHistories()
  }
})

const loadVideoHistories = async () => {
  await historyStore.fetchHistories({
    filter_by: 'video',
    items_per_page: 50,
    page: 1
  })
}

const selectVideo = (video: any) => {
  selectedVideo.value = video
}

const confirmSelection = () => {
  if (selectedVideo.value) {
    emit('video-selected', selectedVideo.value)
    isOpen.value = false
    selectedVideo.value = null
  }
}

const closeModal = () => {
  isOpen.value = false
  selectedVideo.value = null
  searchQuery.value = ''
}

// Get video thumbnail
const getVideoThumbnail = (video: any) => {
  const lastGenerated = video.generated_video?.[video.generated_video.length - 1]
  return lastGenerated?.thumbnail_url || lastGenerated?.video_url
}

// Get video duration
const getVideoDuration = (video: any) => {
  const lastGenerated = video.generated_video?.[video.generated_video.length - 1]
  if (lastGenerated?.duration) {
    const minutes = Math.floor(lastGenerated.duration / 60)
    const seconds = Math.floor(lastGenerated.duration % 60)
    return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`
  }
  return '0:00'
}

// Format date
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

onMounted(() => {
  if (props.modelValue) {
    loadVideoHistories()
  }
})
</script>

<template>
  <UModal
    v-model:open="isOpen"
    :ui="{
      width: 'w-full sm:max-w-4xl',
      height: 'h-[80vh]'
    }"
  >
    <UCard
      :ui="{
        body: 'flex flex-col h-full',
        header: 'pb-4'
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">
            {{ t('videoGen.selectVideoToExtend') }}
          </h3>
          <UButton
            icon="i-lucide-x"
            color="neutral"
            variant="ghost"
            size="sm"
            @click="closeModal"
          />
        </div>
        
        <!-- Search input -->
        <div class="mt-4">
          <UInput
            v-model="searchQuery"
            :placeholder="t('videoGen.searchVideos')"
            icon="i-lucide-search"
            class="w-full"
          />
        </div>
      </template>

      <!-- Video list -->
      <div class="flex-1 overflow-y-auto">
        <div
          v-if="loadings.fetchHistories"
          class="flex items-center justify-center h-40"
        >
          <div class="text-gray-400 dark:text-gray-600 flex flex-col items-center">
            <UIcon
              name="eos-icons:loading"
              class="w-8 h-8 mb-2"
            />
            {{ t('Loading videos...') }}
          </div>
        </div>

        <div
          v-else-if="filteredVideoHistories.length === 0"
          class="flex items-center justify-center h-40"
        >
          <div class="text-gray-400 dark:text-gray-600 text-center">
            <UIcon
              name="i-lucide-video-off"
              class="w-8 h-8 mb-2 mx-auto"
            />
            <p>{{ t('videoGen.noVideosFound') }}</p>
          </div>
        </div>

        <div
          v-else
          class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4"
        >
          <div
            v-for="video in filteredVideoHistories"
            :key="video.uuid"
            class="border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md"
            :class="{
              'border-primary-500 bg-primary-50 dark:bg-primary-900/20': selectedVideo?.uuid === video.uuid,
              'border-gray-200 dark:border-gray-700': selectedVideo?.uuid !== video.uuid
            }"
            @click="selectVideo(video)"
          >
            <div class="flex gap-3">
              <!-- Video thumbnail -->
              <div class="flex-shrink-0">
                <div class="w-20 h-12 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden">
                  <img
                    v-if="getVideoThumbnail(video)"
                    :src="getVideoThumbnail(video)"
                    :alt="video.input_text"
                    class="w-full h-full object-cover"
                  >
                  <div
                    v-else
                    class="w-full h-full flex items-center justify-center"
                  >
                    <UIcon
                      name="i-lucide-video"
                      class="w-6 h-6 text-gray-400"
                    />
                  </div>
                </div>
              </div>

              <!-- Video info -->
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ video.input_text }}
                </h4>
                <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 space-y-1">
                  <div>{{ t('Model') }}: {{ video.model_name }}</div>
                  <div>{{ t('Duration') }}: {{ getVideoDuration(video) }}</div>
                  <div>{{ t('Created') }}: {{ formatDate(video.created_at) }}</div>
                </div>
              </div>

              <!-- Selection indicator -->
              <div class="flex-shrink-0">
                <UIcon
                  v-if="selectedVideo?.uuid === video.uuid"
                  name="i-lucide-check-circle"
                  class="w-5 h-5 text-primary-500"
                />
                <UIcon
                  v-else
                  name="i-lucide-circle"
                  class="w-5 h-5 text-gray-300 dark:text-gray-600"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <UButton
            color="neutral"
            variant="outline"
            @click="closeModal"
          >
            {{ t('Cancel') }}
          </UButton>
          <UButton
            color="primary"
            :disabled="!selectedVideo"
            @click="confirmSelection"
          >
            {{ t('videoGen.selectVideo') }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
