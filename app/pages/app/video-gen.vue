<script setup lang="ts">
import { formatNumber } from '~/utils'
import {
  compareImageArrays,
  commonValidationRules
} from '~/utils/generationValidation'

// SEO meta tags for AI video generator page
useSeoMeta({
  title: 'AI Video Generator - Create Videos from Text with GeminiGen AI',
  description:
    'Generate high-quality AI videos from text prompts using advanced Veo models. Create professional videos, animations, and visual content with AI.',
  ogTitle: 'AI Video Generator - GeminiGen AI',
  ogDescription:
    'Transform text into stunning videos with our AI-powered video generation tool. Multiple models and customization options available.',
  keywords:
    'AI video generator, text to video, AI video creation, artificial intelligence videos, Veo video generation, video AI'
})

// Add structured data for video generator page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        'name': 'GeminiGen AI Video Generator',
        'description':
          'AI-powered video generation tool using advanced Veo models',
        'applicationCategory': 'MultimediaApplication',
        'operatingSystem': 'Web Browser',
        'featureList': [
          'Text-to-Video Generation',
          'Multiple AI Models (Veo 2, Veo 3)',
          'Custom Video Duration',
          'Various Aspect Ratios',
          'High-Quality Video Output',
          'Image-to-Video Conversion'
        ],
        'offers': {
          '@type': 'Offer',
          'description': 'Premium video generation service',
          'priceCurrency': 'USD'
        }
      })
    }
  ]
})

interface ImageFile {
  src: string
  alt: string
  file: File
}
const { authorize } = useAuthorize()
const authStore = useAuthStore()
const user_credit = computed(() => authStore.$state.user?.user_credit)
const isAuthenticated = computed(
  () => !!authStore.$state.access_token && !!authStore.$state.user
)

// Check if user should see the Hot Promotion Banner
// Show banner for: non-logged-in users OR free users
// Hide banner for: premium users
const shouldShowHotPromoBanner = computed(() => {
  // If not authenticated, show banner
  if (!isAuthenticated.value) {
    return true
  }

  // If authenticated, check user plan
  const user = authStore.$state.user
  if (!user || !user.user_plan) {
    return true // Show for users without plan (treat as free)
  }

  // Hide banner only for premium users (PP0001)
  const isPremium = user.user_plan.product?.id === 'PP0001'
  return !isPremium
})
const { model, models } = useVideoGenModels()
const {
  duration,
  durationOptions, // eslint-disable-line @typescript-eslint/no-unused-vars
  isDurationSelectable, // eslint-disable-line @typescript-eslint/no-unused-vars
  enhancePrompt,
  isEnhancePromptLocked
} = useVideoGenOptions()
const { videoDimension } = useVideoDimensions()
const { resolution, isResolutionSelectable } = useVideoResolution()
const { isServerSelectionEnabled, serviceMode }
  = useServerSelection()
const videoStyle = ref('Cinematic')
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig() // eslint-disable-line @typescript-eslint/no-unused-vars
const textToVideoStore = useTextToVideoStore()
const {
  textToVideoResult,
  aiToolVideoCardRef,
  prompt,
  negativePrompt,
  loadings,
  errors
} = storeToRefs(textToVideoStore)

// Video Extend functionality
const isVideoExtendMode = ref(false)
const selectedVideoForExtend = ref<any>(null)
const showVideoExtendModal = ref(false)

// Check if we're in extend mode from route query
onMounted(() => {
  const route = useRoute()
  if (route.query.extend && route.query.video_uuid) {
    // Load the video for extending
    loadVideoForExtend(route.query.video_uuid as string)
  }
})

const loadVideoForExtend = async (videoUuid: string) => {
  const historyStore = useHistoryStore()
  const videoDetail = await historyStore.fetchHistoryDetail(videoUuid)

  if (videoDetail && videoDetail.type === 'video') {
    selectedVideoForExtend.value = videoDetail
    isVideoExtendMode.value = true

    // Set form values based on the selected video
    const lastGenerated = videoDetail.generated_video?.[videoDetail.generated_video.length - 1]
    if (lastGenerated) {
      model.value = models.find(m => m.value === videoDetail.model_name) || models[0]
      videoDimension.value = lastGenerated.aspect_ratio || '16:9'
      if (lastGenerated.resolution) {
        resolution.value = lastGenerated.resolution
      }
    }

    // Focus on prompt input
    nextTick(() => {
      const promptInput = document.querySelector('textarea[placeholder*="Describe"]')
      if (promptInput && promptInput instanceof HTMLElement) {
        promptInput.focus()
      }
    })
  }
}

const openVideoExtendModal = () => {
  showVideoExtendModal.value = true
}

const onVideoSelected = (video: any) => {
  selectedVideoForExtend.value = video
  isVideoExtendMode.value = true

  // Set form values based on the selected video
  const lastGenerated = video.generated_video?.[video.generated_video.length - 1]
  if (lastGenerated) {
    model.value = models.find(m => m.value === video.model_name) || models[0]
    videoDimension.value = lastGenerated.aspect_ratio || '16:9'
    if (lastGenerated.resolution) {
      resolution.value = lastGenerated.resolution
    }
  }

  // Clear prompt to focus on new extend prompt
  prompt.value = ''

  // Focus on prompt input
  nextTick(() => {
    const promptInput = document.querySelector('textarea[placeholder*="Describe"]')
    if (promptInput && promptInput instanceof HTMLElement) {
      promptInput.focus()
    }
  })
}

const exitVideoExtendMode = () => {
  isVideoExtendMode.value = false
  selectedVideoForExtend.value = null

  // Clear route query if present
  const route = useRoute()
  if (route.query.extend || route.query.video_uuid) {
    router.push({ query: {} })
  }
}

// Override default values for hidden fields
duration.value = 8 // Force 8 seconds
enhancePrompt.value = true // Force enhance prompt on
// Removed productStore imports as we now use fixed pricing

// Calculate actual cost based on model, resolution, and server type
const actualCost = computed(() => {
  const isVipServer = serviceMode.value === 'stable'

  // Base pricing for different models and server types
  const getBasePrice = (modelValue: string) => {
    switch (modelValue) {
      case 'veo-2':
        return isVipServer ? 800 : 20 // VIP: 800 credits (original), Regular: 20 credits (discounted)
      case 'veo-3-fast':
        return isVipServer ? 640 : 20
      case 'veo-3':
        return isVipServer ? 1200 : 100 // VIP: 640 credits (original), Regular: 20 credits (discounted)
      default:
        return isVipServer ? 800 : 20 // Default to veo-2 pricing
    }
  }

  const basePrice = getBasePrice(model.value?.value || 'veo-2')

  // Full HD pricing adjustments (only for 16:9 aspect ratio)
  if (resolution.value === '1080p' && videoDimension.value === '16:9') {
    switch (model.value?.value) {
      case 'veo-3-fast':
        return isVipServer ? 640 : 20 // VIP: 640 credits (original), Regular: 70 credits (discounted)
      case 'veo-3':
        return isVipServer ? 1200 : 100 // VIP: 1200 credits (original), Regular: 200 credits (discounted)
      default:
        return basePrice
    }
  }

  // For 9:16, always use base price (720p only)
  // No additional pricing for 9:16 as it only supports 720p

  return basePrice
})
// Local state for selected images
const selectedImages = ref<ImageFile[]>([])

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  negativePrompt: '',
  model: models[0],
  videoDimension: '16:9',
  videoStyle: 'Cinematic',
  enhancePrompt: false,
  resolution: models[0]?.resolutions?.default || '720p',
  selectedImages: [] as ImageFile[]
})

// Initialize initial values on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    negativePrompt: negativePrompt.value,
    model: model.value,
    videoDimension: videoDimension.value,
    videoStyle: videoStyle.value,
    enhancePrompt: enhancePrompt.value,
    resolution: resolution.value,
    selectedImages: [...selectedImages.value]
  }
})

// Note: Resolution reset logic is now handled in useVideoResolution composable

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = prompt.value !== initialValues.value.prompt
      || negativePrompt.value !== initialValues.value.negativePrompt
      || model.value?.value !== initialValues.value.model?.value
      || videoDimension.value !== initialValues.value.videoDimension
      || videoStyle.value !== initialValues.value.videoStyle
      || enhancePrompt.value !== initialValues.value.enhancePrompt
      || resolution.value !== initialValues.value.resolution

  // Image comparison with better performance
  const imagesChanged = compareImageArrays(
    selectedImages.value,
    initialValues.value.selectedImages
  )

  return basicFieldsChanged || imagesChanged
})

// Handle image selection
const handleImagesSelected = (images: ImageFile[]) => {
  selectedImages.value = images
  // Also update store for backward compatibility
  textToVideoStore.selectedImages = images
}

// Helper function to perform the actual generation
const performGeneration = async () => {
  let result

  if (isVideoExtendMode.value && selectedVideoForExtend.value) {
    // Video extend mode
    result = await textToVideoStore.videoExtend({
      prompt: prompt.value,
      ref_history: selectedVideoForExtend.value.uuid,
      service_mode: isServerSelectionEnabled.value
        ? serviceMode.value
        : 'unstable'
    })
  } else {
    // Normal video generation mode
    // Extract File objects from selected images
    const files = selectedImages.value.map(img => img.file).filter(Boolean)

    result = await textToVideoStore.textToVideo({
      prompt: prompt.value,
      negative_prompt: negativePrompt.value,
      model: model.value?.value || 'veo-2',
      aspect_ratio: videoDimension.value || '16:9',
      enhance_prompt: enhancePrompt.value,
      duration: duration.value,
      resolution: (isResolutionSelectable.value && (videoDimension.value === '16:9' || videoDimension.value === '9:16')) ? resolution.value : undefined,
      service_mode: isServerSelectionEnabled.value
        ? serviceMode.value
        : 'unstable',
      files: files
    })
  }

  if (result) {
    let generationType, generationMessage

    if (isVideoExtendMode.value) {
      generationType = t('videoGen.videoExtend')
      generationMessage = t('videoGen.videoExtendGenerationStarted')
    } else {
      generationType = selectedImages.value.length > 0
        ? t('videoGen.imageToVideo')
        : t('videoGen.textToVideo')
      generationMessage = selectedImages.value.length > 0
        ? t('videoGen.imageToVideoGenerationStarted')
        : t('videoGen.textToVideoGenerationStarted')
    }

    toast.add({
      id: 'success',
      title: `${generationType} ${t('videoGen.generated')}`,
      description: generationMessage,
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      negativePrompt: negativePrompt.value,
      model: model.value,
      videoDimension: videoDimension.value,
      videoStyle: videoStyle.value,
      enhancePrompt: enhancePrompt.value,
      resolution: resolution.value,
      selectedImages: [...selectedImages.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value,
      t('videoGen.pleaseEnterPrompt')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'video',
    hasChanges,
    hasResult: computed(() => !!textToVideoResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const onUsePrompt = (newPrompt: string) => {
  prompt.value = newPrompt
  // scroll to top and focus on prompt input
  nextTick(() => {
    // scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' })

    // try to focus the prompt input after scrolling
    setTimeout(() => {
      // look for the prompt input (UChatPrompt component)
      const promptInput = document.querySelector(
        '[class*="chat-prompt"] textarea, [class*="chat-prompt"] input'
      )
      if (promptInput && promptInput instanceof HTMLElement) {
        promptInput.focus()
      }
    }, 500)
  })
}

// Keep old functions for potential future use (currently not used in UI)
const addNegativePromptSuggestion = (suggestion: string) => { // eslint-disable-line @typescript-eslint/no-unused-vars
  if (negativePrompt.value) {
    // If there's already content, add a comma and space before the new suggestion
    negativePrompt.value += ', ' + suggestion
  } else {
    // If empty, just add the suggestion
    negativePrompt.value = suggestion
  }
}

const enhancePromptItems = computed(() => { // eslint-disable-line @typescript-eslint/no-unused-vars
  const items = [
    {
      label: t('On'),
      value: 'true',
      description: isEnhancePromptLocked.value
        ? t('videoGen.enhancePromptOnRequired')
        : t('videoGen.enhancePromptOn'),
      disabled: isEnhancePromptLocked.value && !enhancePrompt.value
    },
    {
      label: t('Off'),
      value: 'false',
      description: t('videoGen.enhancePromptOff'),
      disabled: isEnhancePromptLocked.value && enhancePrompt.value
    }
  ]

  return items
})

// Convert enhancePrompt to string for radio group
const enhancePromptString = computed({ // eslint-disable-line @typescript-eslint/no-unused-vars
  get: () => enhancePrompt.value.toString(),
  set: (value: string) => {
    enhancePrompt.value = value === 'true'
  }
})

// Handle buy credits action
const handleBuyCredits = () => {
  errors.value.textToVideo = null
  navigateTo('/profile/credits')
}

const videoInsufficientCreditsMessage = computed(() => {
  const isVipServer = serviceMode.value === 'stable'

  return t('NOT_ENOUGH_CREDIT_MESSAGE_VIDEO_C', {
    money: 10,
    credit: '2000',
    // veo-3-fast pricing
    veo3FastCredit: formatNumber(isVipServer ? 1200 : 100), // VIP: 1200 credits, Regular: 100 credits
    veo3FastPrice: isVipServer ? 6.00 : 0.50, // VIP: $6.00, Regular: $0.50
    veo3FastFullHDCredit: formatNumber(isVipServer ? 640 : 70), // VIP: 640 credits, Regular: 70 credits
    veo3FastFullHDPrice: isVipServer ? 3.20 : 0.35, // VIP: $3.20, Regular: $0.35
    // veo-3 pricing
    veo3Credit: formatNumber(isVipServer ? 640 : 20), // VIP: 640 credits, Regular: 20 credits
    veo3Price: isVipServer ? 3.20 : 0.10, // VIP: $3.20, Regular: $0.10
    veo3FullHDCredit: formatNumber(isVipServer ? 1200 : 200), // VIP: 1200 credits, Regular: 200 credits
    veo3FullHDPrice: isVipServer ? 6.00 : 1.00, // VIP: $6.00, Regular: $1.00
    // veo-2 pricing
    veo2Credit: formatNumber(isVipServer ? 800 : 20), // VIP: 800 credits, Regular: 20 credits
    veo2Price: isVipServer ? 4.00 : 0.10, // VIP: $4.00, Regular: $0.10
    save: 97
  })
})
</script>

<template>
  <UContainer class="mt-0">
    <!-- SEO H1 Heading -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        {{ $t("AI Video Generator") }}
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
        {{
          $t(
            "Create professional videos from text prompts using advanced AI models"
          )
        }}
      </p>

      <!-- Hot Promotion Banner - Only show for free users or non-logged-in users -->
      <div
        v-if="shouldShowHotPromoBanner"
        class="mt-6 mb-4"
      >
        <div class="relative inline-block">
          <!-- Burning banner with fire effects -->
          <div class="burning-banner relative inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white font-bold text-md rounded-full shadow-lg transform hover:scale-105 transition-all duration-300">
            <!-- Fire flames coming from banner -->
            <div class="fire-container absolute inset-0 overflow-hidden rounded-full">
              <!-- Top flames - varied heights -->
              <div class="fire-flame flame-short fire-1 absolute -top-1 left-1/12" />
              <div class="fire-flame flame-medium fire-2 absolute -top-2 left-1/6" />
              <div class="fire-flame flame-tall fire-3 absolute -top-4 left-1/4" />
              <div class="fire-flame flame-short fire-4 absolute -top-1 left-1/3" />
              <div class="fire-flame flame-medium fire-5 absolute -top-3 left-5/12" />
              <div class="fire-flame flame-tall fire-6 absolute -top-4 left-1/2" />
              <div class="fire-flame flame-short fire-7 absolute -top-2 left-7/12" />
              <div class="fire-flame flame-medium fire-8 absolute -top-3 right-1/3" />
              <div class="fire-flame flame-tall fire-9 absolute -top-4 right-1/4" />
              <div class="fire-flame flame-short fire-10 absolute -top-1 right-1/6" />
              <div class="fire-flame flame-medium fire-11 absolute -top-2 right-1/12" />

              <!-- Side flames -->
              <div class="fire-flame flame-medium fire-12 absolute top-1/4 -left-1 transform rotate-90" />
              <div class="fire-flame flame-short fire-13 absolute top-3/4 -left-1 transform rotate-90" />
              <div class="fire-flame flame-medium fire-14 absolute top-1/4 -right-1 transform -rotate-90" />
              <div class="fire-flame flame-short fire-15 absolute top-3/4 -right-1 transform -rotate-90" />

              <!-- Bottom flames -->
              <div class="fire-flame flame-short fire-16 absolute -bottom-1 left-1/4 transform rotate-180" />
              <div class="fire-flame flame-short fire-17 absolute -bottom-1 right-1/4 transform rotate-180" />

              <!-- Ember particles -->
              <div class="ember ember-1 absolute -top-3 left-1/5" />
              <div class="ember ember-2 absolute -top-4 right-1/5" />
              <div class="ember ember-3 absolute -top-2 left-3/5" />
              <div class="ember ember-4 absolute -top-5 left-2/3" />
            </div>

            <!-- Banner content -->
            <span class="relative z-10">{{ $t("videoGen.hotPromo") }}</span>
            <span class="ml-2 text-xl relative z-10">✨</span>
          </div>
        </div>
      </div>
    </div>

    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                :disabled="isVideoExtendMode"
                class="w-full"
              />
            </UFormField>
            <div
              v-if="model?.options?.includes('yourImage')"
              class="flex flex-row gap-3 items-end"
            >
              <UFormField :label="$t('videoGen.imageReference')">
                <BaseImageSelect
                  v-model="selectedImages"
                  @update:model-value="handleImagesSelected"
                />
              </UFormField>
              <BaseImageSelectedList
                v-model="selectedImages"
                @update:model-value="handleImagesSelected"
              />
            </div>
          </div>

          <!-- Video Extend Mode Indicator -->
          <div
            v-if="isVideoExtendMode && selectedVideoForExtend"
            class="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <UIcon
                  name="i-lucide-video"
                  class="w-5 h-5 text-blue-600"
                />
                <div>
                  <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">
                    {{ $t('videoGen.extendingVideo') }}
                  </h4>
                  <p class="text-xs text-blue-700 dark:text-blue-300 truncate max-w-md">
                    {{ selectedVideoForExtend.input_text }}
                  </p>
                </div>
              </div>
              <UButton
                icon="i-lucide-x"
                color="neutral"
                variant="ghost"
                size="xs"
                @click="exitVideoExtendMode"
              />
            </div>
          </div>

          <UFormField :label="$t('Prompt')">
            <template #hint>
              <div class="flex items-center gap-2">
                <UButton
                  v-if="!isVideoExtendMode"
                  icon="i-lucide-video-plus"
                  color="primary"
                  variant="outline"
                  size="xs"
                  :label="$t('videoGen.videoExtend')"
                  @click="openVideoExtendModal"
                />
              </div>
            </template>
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="isVideoExtendMode
                ? $t('videoGen.extendPromptPlaceholder')
                : $t('Describe the video you want to generate...')"
              :rows="6"
            />
          </UFormField>

          <!-- HIDDEN: Negative Prompt Section -->
          <!--
          <div>
            <UFormField :label="$t('videoGen.negativePrompt')">
              <template #hint>
                <UTooltip
                  :delay-duration="0"
                  :text="$t('videoGen.negativePromptTooltip')"
                >
                  <UIcon name="material-symbols:help" />
                </UTooltip>
              </template>
              <UTextarea
                v-model="negativePrompt"
                class="w-full"
                :placeholder="$t('videoGen.negativePromptPlaceholder')"
                :rows="3"
              />
              <template #description>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ $t("videoGen.negativePromptDescription") }}
                </span>
                <div class="space-y-2 mt-0">
                  <div
                    class="text-xs font-medium text-gray-600 dark:text-gray-300"
                  >
                    {{ $t("videoGen.negativePromptSuggestions") }}:
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <UButton
                      v-for="i in 6"
                      :key="i"
                      size="xs"
                      variant="outline"
                      color="neutral"
                      :label="$t(`videoGen.negativePromptSuggestion${i}`)"
                      @click="
                        addNegativePromptSuggestion(
                          $t(`videoGen.negativePromptSuggestion${i}`)
                        )
                      "
                    />
                  </div>
                </div>
              </template>
            </UFormField>
          </div>
          -->
          <div class="space-y-6">
            <!-- HIDDEN: First row: Enhance Prompt and Aspect Ratio -->
            <!--
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField :label="$t('videoGen.enhancePrompt')">
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="
                      isEnhancePromptLocked
                        ? $t('videoGen.enhancePromptLocked')
                        : enhancePrompt
                          ? $t('videoGen.enhancePromptOn')
                          : $t('videoGen.enhancePromptNotRefined')
                    "
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <URadioGroup
                  v-model="enhancePromptString"
                  orientation="horizontal"
                  variant="card"
                  value-key="value"
                  :items="enhancePromptItems"
                  size="xs"
                  :disabled="isEnhancePromptLocked"
                />
              </UFormField>

              <UFormField :label="$t('videoGen.aspectRatio')">
                <BaseVideoDimensionsSelect :options="model?.ratios" />
              </UFormField>
            </div>
            -->

            <!-- Aspect Ratio and Resolution -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField :label="$t('videoGen.aspectRatio')">
                <BaseVideoDimensionsSelect
                  :options="model?.ratios"
                  :disabled="isVideoExtendMode"
                />
              </UFormField>

              <UFormField
                v-if="isResolutionSelectable && (videoDimension === '16:9' || videoDimension === '9:16')"
                :label="$t('videoGen.resolution')"
              >
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="$t('videoGen.selectResolution')"
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <BaseVideoResolutionSelect
                  :disabled="isVideoExtendMode"
                  class=""
                />
              </UFormField>
            </div>

            <!-- Server Selection -->
            <UFormField
              v-if="isServerSelectionEnabled"
              :label="$t('Server')"
            >
              <BaseServerSelect />
            </UFormField>
          </div>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div
              class="text-xs text-right space-y-1"
            >
              <div>
                {{
                  $t("videoGen.creditsRemaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary font-medium">
                {{
                  $t("videoGen.generationCostPerVideo", {
                    cost: formatNumber(actualCost)
                  })
                }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ $t("videoGen.fixedPricePerVideo") }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="isVideoExtendMode ? $t('videoGen.extendVideo') : $t('videoGen.generateVideo')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-success-600 !cursor-pointer"
              trailing-icon="line-md:arrow-right"
              :loading="loadings['textToVideo'] || loadings['videoExtend']"
              :disabled="!prompt"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToVideoResult || loadings['textToVideo'] || loadings['videoExtend'])
            && !errors['textToVideo'] && !errors['videoExtend']
        "
        ref="aiToolVideoCardRef"
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolVideoCard
          v-bind="textToVideoResult"
          :data="textToVideoResult"
          :loading="loadings['textToVideo'] || loadings['videoExtend']"
          class="h-full"
        />
      </Motion>
      <UCard
        v-else
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div
          v-if="errors['textToVideo'] || errors['videoExtend']"
          class="flex flex-col items-center justify-center h-full p-8"
        >
          <!-- Enhanced display for NOT_ENOUGH_AND_LOCK_CREDIT error -->
          <InsufficientCreditsError
            v-if="
              ['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(
                errors['textToVideo'] || errors['videoExtend']
              )
            "
            :credits-needed="actualCost"
            :available-credits="user_credit?.available_credit || 0"
            generation-type="video"
            :message="videoInsufficientCreditsMessage"
            @buy-credits="handleBuyCredits"
            @view-pricing="$router.push('/pricing')"
          />

          <!-- Default error display for other errors -->
          <div
            v-else
            class="text-center space-y-4"
          >
            <div>
              <UIcon
                name="i-lucide-alert-circle"
                class="text-6xl mb-2 text-error"
              />
            </div>
            <div class="text-sm text-error">
              {{ $t(errors["textToVideo"] || errors["videoExtend"] || "videoGen.somethingWentWrong") }}
            </div>
          </div>
        </div>
        <div
          v-else
          class="h-full"
        >
          <div class="mb-4 text-center">
            <h3
              class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
            >
              {{ $t("videoGen.examplesTitle") }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ $t("videoGen.examplesDescription") }}
            </p>
          </div>
          <BaseVideoComparisonDemo :height="'350px'" />
        </div>
      </UCard>
    </div>
    <!-- Video Prompt Gallery -->
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.6,
        delay: 1.2
      }"
    >
      <VideoPromptGallery
        class="mt-8"
        @use-prompt="onUsePrompt"
      />
    </Motion>

    <!-- Buy Credits Drawer -->
    <BuyCreditsDrawer />

    <!-- Video Extend Modal -->
    <VideoExtendModal
      v-model="showVideoExtendModal"
      @video-selected="onVideoSelected"
    />
  </UContainer>
</template>

<style scoped>
/* Burning banner effect - reduced intensity */
.burning-banner {
  position: relative;
  animation: bannerBurn 3s ease-in-out infinite;
  box-shadow:
    0 0 15px rgba(255, 69, 0, 0.4),
    0 0 25px rgba(255, 69, 0, 0.3),
    inset 0 0 10px rgba(255, 140, 0, 0.2);
}

.fire-container {
  pointer-events: none;
}

/* Fire flames coming from banner - varied sizes */
.fire-flame {
  background: linear-gradient(to top,
    rgba(255, 69, 0, 0.6) 0%,
    rgba(255, 100, 0, 0.5) 20%,
    rgba(255, 140, 0, 0.4) 50%,
    rgba(255, 180, 0, 0.3) 70%,
    rgba(255, 220, 0, 0.2) 90%,
    transparent 100%);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  transform-origin: bottom center;
  animation: flameFlicker 1s ease-in-out infinite alternate;
}

/* Different flame sizes */
.flame-short {
  width: 4px;
  height: 8px;
}

.flame-medium {
  width: 6px;
  height: 12px;
}

.flame-tall {
  width: 8px;
  height: 18px;
}

/* Individual flame delays for realistic effect */
.fire-1 { animation-delay: 0s; }
.fire-2 { animation-delay: 0.1s; }
.fire-3 { animation-delay: 0.2s; }
.fire-4 { animation-delay: 0.3s; }
.fire-5 { animation-delay: 0.4s; }
.fire-6 { animation-delay: 0.5s; }
.fire-7 { animation-delay: 0.6s; }
.fire-8 { animation-delay: 0.7s; }
.fire-9 { animation-delay: 0.8s; }
.fire-10 { animation-delay: 0.9s; }
.fire-11 { animation-delay: 1s; }
.fire-12 { animation-delay: 0.15s; }
.fire-13 { animation-delay: 0.25s; }
.fire-14 { animation-delay: 0.35s; }
.fire-15 { animation-delay: 0.45s; }
.fire-16 { animation-delay: 0.55s; }
.fire-17 { animation-delay: 0.65s; }

/* Ember particles - softer effect */
.ember {
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, rgba(255, 170, 0, 0.6) 0%, rgba(255, 100, 0, 0.4) 50%, transparent 100%);
  border-radius: 50%;
  animation: emberFloat 2s ease-in-out infinite;
}

.ember-1 { animation-delay: 0s; }
.ember-2 { animation-delay: 0.5s; }
.ember-3 { animation-delay: 1s; }
.ember-4 { animation-delay: 1.5s; }

/* Banner burning animation - gentler effect */
@keyframes bannerBurn {
  0% {
    filter: brightness(1) contrast(1) saturate(1);
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.4),
      0 0 25px rgba(255, 69, 0, 0.3),
      inset 0 0 10px rgba(255, 140, 0, 0.2);
  }
  25% {
    filter: brightness(1.1) contrast(1.05) saturate(1.1);
    box-shadow:
      0 0 18px rgba(255, 69, 0, 0.5),
      0 0 30px rgba(255, 69, 0, 0.4),
      inset 0 0 12px rgba(255, 140, 0, 0.25);
  }
  50% {
    filter: brightness(0.95) contrast(0.98) saturate(0.9);
    box-shadow:
      0 0 12px rgba(255, 69, 0, 0.35),
      0 0 20px rgba(255, 69, 0, 0.25),
      inset 0 0 8px rgba(255, 140, 0, 0.15);
  }
  75% {
    filter: brightness(1.15) contrast(1.08) saturate(1.15);
    box-shadow:
      0 0 20px rgba(255, 69, 0, 0.6),
      0 0 35px rgba(255, 69, 0, 0.45),
      inset 0 0 15px rgba(255, 140, 0, 0.3);
  }
  100% {
    filter: brightness(1) contrast(1) saturate(1);
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.4),
      0 0 25px rgba(255, 69, 0, 0.3),
      inset 0 0 10px rgba(255, 140, 0, 0.2);
  }
}

/* Flame flickering animation */
@keyframes flameFlicker {
  0% {
    transform: scaleY(1) scaleX(1) rotate(-2deg);
    opacity: 0.8;
  }
  25% {
    transform: scaleY(1.3) scaleX(0.8) rotate(1deg);
    opacity: 0.9;
  }
  50% {
    transform: scaleY(0.7) scaleX(1.2) rotate(-1deg);
    opacity: 0.7;
  }
  75% {
    transform: scaleY(1.4) scaleX(0.7) rotate(2deg);
    opacity: 0.95;
  }
  100% {
    transform: scaleY(0.9) scaleX(1.1) rotate(-1deg);
    opacity: 0.6;
  }
}

/* Ember floating animation */
@keyframes emberFloat {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-20px) scale(0.5);
    opacity: 0;
  }
}
</style>
